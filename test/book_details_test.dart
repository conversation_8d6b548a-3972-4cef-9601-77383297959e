// Book Details Screen Widget Test
//
// This test verifies the book details screen functionality and UI components.

import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/books/data/models/publisher.dart';
import 'package:e_library/features/books/ui/screens/book_details_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  // Create test data
  const testAuthor = Author(
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    country: 'USA',
  );

  const testPublisher = Publisher(
    id: 1,
    name: 'Test Publisher',
  );

  const testBook = Book(
    id: 1,
    title: 'Test Book Title',
    category: 'Fiction',
    price: 29.99,
    publisherId: 1,
    authorId: 1,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
    author: test<PERSON><PERSON><PERSON>,
    publisher: testPublisher,
  );

  testWidgets('Book details screen should show error when no arguments provided', (WidgetTester tester) async {
    // Build the book details screen without arguments
    await tester.pumpWidget(
      MaterialApp(
        home: const BookDetailsScreen(),
      ),
    );

    // Wait for the widget to build
    await tester.pump();

    // Verify error state is shown
    expect(find.text('Failed to load book details'), findsOneWidget);
    expect(find.text('No book information provided'), findsOneWidget);
    expect(find.text('Retry'), findsOneWidget);
  });

  testWidgets('Book details screen should display book information correctly', (WidgetTester tester) async {
    // Build the book details screen with book data
    await tester.pumpWidget(
      MaterialApp(
        routes: {
          '/book-details': (context) => const BookDetailsScreen(),
        },
        home: Builder(
          builder: (context) => Scaffold(
            body: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pushNamed(
                  '/book-details',
                  arguments: {
                    'bookId': testBook.id,
                    'book': testBook,
                  },
                );
              },
              child: const Text('Navigate'),
            ),
          ),
        ),
      ),
    );

    // Navigate to book details
    await tester.tap(find.text('Navigate'));
    await tester.pumpAndSettle();

    // Verify book details are displayed
    expect(find.text('Test Book Title'), findsAtLeastNWidgets(1)); // Title appears in app bar and content
    expect(find.text('FICTION'), findsOneWidget);
    expect(find.text('\$29.99'), findsOneWidget);
    
    // Verify author information
    expect(find.text('Author'), findsOneWidget);
    expect(find.text('John Doe'), findsOneWidget);
    expect(find.text('USA'), findsOneWidget);
    
    // Verify publisher information
    expect(find.text('Publisher'), findsOneWidget);
    expect(find.text('Test Publisher'), findsOneWidget);
    
    // Verify additional information
    expect(find.text('Additional Information'), findsOneWidget);
    expect(find.text('#1'), findsOneWidget); // Book ID
  });

  testWidgets('Book details screen should show error for invalid book ID', (WidgetTester tester) async {
    // Build the book details screen with invalid arguments
    await tester.pumpWidget(
      MaterialApp(
        routes: {
          '/book-details': (context) => const BookDetailsScreen(),
        },
        home: Builder(
          builder: (context) => Scaffold(
            body: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pushNamed(
                  '/book-details',
                  arguments: {
                    'bookId': null, // Invalid book ID
                  },
                );
              },
              child: const Text('Navigate'),
            ),
          ),
        ),
      ),
    );

    // Navigate to book details
    await tester.tap(find.text('Navigate'));
    await tester.pumpAndSettle();

    // Should show error state for invalid book ID
    expect(find.text('Failed to load book details'), findsOneWidget);
    expect(find.text('No book ID provided'), findsOneWidget);
    expect(find.text('Retry'), findsOneWidget);
  });

  testWidgets('Book details screen should handle missing optional data gracefully', (WidgetTester tester) async {
    // Create book with minimal data
    const minimalBook = Book(
      id: 2,
      title: 'Minimal Book',
      category: 'Non-Fiction',
      price: 15.50,
      publisherId: 2,
      authorId: 2,
      // No createdAt, updatedAt, author, or publisher objects
    );

    await tester.pumpWidget(
      MaterialApp(
        routes: {
          '/book-details': (context) => const BookDetailsScreen(),
        },
        home: Builder(
          builder: (context) => Scaffold(
            body: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pushNamed(
                  '/book-details',
                  arguments: {
                    'bookId': minimalBook.id,
                    'book': minimalBook,
                  },
                );
              },
              child: const Text('Navigate'),
            ),
          ),
        ),
      ),
    );

    // Navigate to book details
    await tester.tap(find.text('Navigate'));
    await tester.pumpAndSettle();

    // Verify basic information is displayed
    expect(find.text('Minimal Book'), findsAtLeastNWidgets(1));
    expect(find.text('NON-FICTION'), findsOneWidget);
    expect(find.text('\$15.50'), findsOneWidget);
    
    // Verify fallback values are used
    expect(find.text('Unknown Author'), findsOneWidget);
    expect(find.text('Unknown Publisher'), findsOneWidget);
    
    // Verify book ID is still shown
    expect(find.text('#2'), findsOneWidget);
  });

  testWidgets('Book details screen should have proper navigation', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        routes: {
          '/book-details': (context) => const BookDetailsScreen(),
        },
        home: Builder(
          builder: (context) => Scaffold(
            body: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pushNamed(
                  '/book-details',
                  arguments: {
                    'bookId': testBook.id,
                    'book': testBook,
                  },
                );
              },
              child: const Text('Navigate'),
            ),
          ),
        ),
      ),
    );

    // Navigate to book details
    await tester.tap(find.text('Navigate'));
    await tester.pumpAndSettle();

    // Verify app bar is present with back button
    expect(find.byType(AppBar), findsOneWidget);
    expect(find.byType(BackButton), findsOneWidget);
    
    // Test back navigation
    await tester.tap(find.byType(BackButton));
    await tester.pumpAndSettle();
    
    // Should be back to the original screen
    expect(find.text('Navigate'), findsOneWidget);
  });
}

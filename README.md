# e_library

A demo e-library application that's supposed to help me explore flutter

## Features

### Book Details Page
- **Comprehensive book information display**: Shows title, category, price, author details, publisher information, and metadata
- **Smart navigation**: Accepts book ID or book object as parameters for optimal user experience
- **Error handling**: Graceful handling of API failures, missing data, and network issues
- **Loading states**: Proper loading indicators while fetching data
- **Responsive design**: Clean, card-based layout following Material 3 design principles
- **Data validation**: <PERSON>les incomplete or missing book information gracefully

### Navigation
- Tap any book in the books list to view detailed information
- Back navigation support with proper app bar integration
- Route-based navigation using named routes (`/book-details`)

### API Integration
- Fetches detailed book information from `/api/books/{id}` endpoint
- Includes nested author and publisher information
- Proper error handling for 404 and network failures
- Retry functionality for failed requests

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
